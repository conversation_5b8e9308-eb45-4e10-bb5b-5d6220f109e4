import os
import time
import tempfile
import uuid
from typing import Optional
from pathlib import Path

# 🔒 Set visible CUDA device to GPU 1 (makes it appear as cuda:0)
os.environ["CUDA_VISIBLE_DEVICES"] = "1"
# 🔧 Disable torch.compile (TorchDynamo) entirely
os.environ["TORCH_DISABLE_DYNAMO"] = "1"
os.environ["NO_TORCH_COMPILE"] = "1"  # Also disable Triton compilation

# 🛡️ Suppress TorchDynamo internal fallback crashes
import torch._dynamo
torch._dynamo.config.suppress_errors = True

import torch
import torchaudio
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from generator import load_csm_1b, Segment

# 🧹 Clear GPU memory before loading model
torch.cuda.empty_cache()
torch.cuda.reset_peak_memory_stats()

app = FastAPI(title="CSM Voice Generation API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables for model
generator = None
device = None

def load_prompt_audio(audio_path: str, target_sample_rate: int) -> torch.Tensor:
    """Load and resample audio to target sample rate."""
    audio_tensor, sample_rate = torchaudio.load(audio_path)
    audio_tensor = audio_tensor.squeeze(0)
    audio_tensor = torchaudio.functional.resample(
        audio_tensor, orig_freq=sample_rate, new_freq=target_sample_rate
    )
    return audio_tensor

def prepare_prompt(text: str, speaker: int, audio_path: str, sample_rate: int) -> Segment:
    """Prepare a voice prompt segment."""
    audio_tensor = load_prompt_audio(audio_path, sample_rate)
    return Segment(text=text, speaker=speaker, audio=audio_tensor)

@app.on_event("startup")
async def startup_event():
    """Initialize the model on startup."""
    global generator, device
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"🚀 Starting API with device: {device}")
    
    print("Loading CSM model...")
    model_load_start = time.time()
    generator = load_csm_1b(device)
    model_load_time = time.time() - model_load_start
    print(f"✅ Model loaded in {model_load_time:.2f} seconds")

@app.get("/")
async def root():
    """Health check endpoint."""
    return {
        "message": "CSM Voice Generation API is running",
        "device": device,
        "model_loaded": generator is not None
    }

@app.post("/generate-voice")
async def generate_voice(
    text: str = Form(..., description="Text to generate speech for"),
    voice_file: UploadFile = File(..., description="Voice sample file (WAV format)"),
    voice_text: str = Form(..., description="Text that corresponds to the voice sample"),
    max_audio_length_ms: Optional[int] = Form(30000, description="Maximum audio length in milliseconds")
):
    """
    Generate speech using the provided voice sample.
    
    - **text**: The text you want to convert to speech
    - **voice_file**: Audio file containing the voice sample (WAV format recommended)
    - **voice_text**: The text that corresponds to the voice sample
    - **max_audio_length_ms**: Maximum length of generated audio in milliseconds
    """
    if generator is None:
        raise HTTPException(status_code=503, detail="Model not loaded yet")
    
    # Validate file type
    if not voice_file.filename.lower().endswith(('.wav', '.mp3', '.flac', '.m4a')):
        raise HTTPException(status_code=400, detail="Voice file must be an audio file (WAV, MP3, FLAC, M4A)")
    
    try:
        # Save uploaded voice file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as temp_voice_file:
            content = await voice_file.read()
            temp_voice_file.write(content)
            temp_voice_path = temp_voice_file.name
        
        # Prepare the voice prompt
        print(f"🎵 Preparing voice prompt from uploaded file...")
        prompt_segment = prepare_prompt(
            text=voice_text,
            speaker=0,
            audio_path=temp_voice_path,
            sample_rate=generator.sample_rate
        )
        
        # Generate speech
        print(f"🎵 Generating speech for: {text[:50]}...")
        generation_start = time.time()
        
        audio_tensor = generator.generate(
            text=text,
            speaker=0,
            context=[prompt_segment],
            max_audio_length_ms=max_audio_length_ms,
        )
        
        generation_time = time.time() - generation_start
        print(f"✅ Speech generated in {generation_time:.2f} seconds")

        # Save generated audio to temporary file
        output_filename = f"generated_speech_{uuid.uuid4().hex[:8]}.wav"
        output_path = f"/tmp/{output_filename}"

        # Audio is already watermarked by the generator, just save it
        torchaudio.save(
            output_path,
            audio_tensor.unsqueeze(0).cpu(),
            generator.sample_rate
        )
        
        # Clean up temporary voice file
        os.unlink(temp_voice_path)
        
        return FileResponse(
            output_path,
            media_type="audio/wav",
            filename=output_filename,
            headers={"X-Generation-Time": str(generation_time)}
        )
        
    except Exception as e:
        # Clean up temporary files on error
        if 'temp_voice_path' in locals() and os.path.exists(temp_voice_path):
            os.unlink(temp_voice_path)
        raise HTTPException(status_code=500, detail=f"Error generating speech: {str(e)}")

@app.get("/health")
async def health_check():
    """Detailed health check."""
    return {
        "status": "healthy",
        "device": device,
        "model_loaded": generator is not None,
        "cuda_available": torch.cuda.is_available(),
        "gpu_memory": torch.cuda.get_device_properties(0).total_memory if torch.cuda.is_available() else None
    }

if __name__ == "__main__":
    uvicorn.run(
        "voice_api:app",
        host="0.0.0.0",
        port=8000,
        reload=False
    )
