import os
import time
import tempfile
import uuid
from typing import Optional
from pathlib import Path

# 🔒 Set visible CUDA device to GPU 1 (makes it appear as cuda:0)
os.environ["CUDA_VISIBLE_DEVICES"] = "1"
# 🔧 Disable torch.compile (TorchDynamo) entirely
os.environ["TORCH_DISABLE_DYNAMO"] = "1"
os.environ["NO_TORCH_COMPILE"] = "1"  # Also disable Triton compilation

# 🛡️ Suppress TorchDynamo internal fallback crashes
import torch._dynamo
torch._dynamo.config.suppress_errors = True

import torch
import torchaudio
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from faster_whisper import WhisperModel
import re

from generator import load_csm_1b, Segment

# 🧹 Clear GPU memory before loading model
torch.cuda.empty_cache()
torch.cuda.reset_peak_memory_stats()

app = FastAPI(title="CSM Voice Generation API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables for models
generator = None
whisper_model = None
device = None

def load_prompt_audio(audio_path: str, target_sample_rate: int) -> torch.Tensor:
    """Load and resample audio to target sample rate."""
    audio_tensor, sample_rate = torchaudio.load(audio_path)
    audio_tensor = audio_tensor.squeeze(0)
    audio_tensor = torchaudio.functional.resample(
        audio_tensor, orig_freq=sample_rate, new_freq=target_sample_rate
    )
    return audio_tensor

def transcribe_and_clean_audio(audio_path: str) -> str:
    """Transcribe audio file and clean the text."""
    global whisper_model

    print(f"🎤 Transcribing audio file...")

    # Transcribe the audio
    segments, info = whisper_model.transcribe(audio_path, beam_size=5)

    print(f"🗣️ Detected language: {info.language}")

    # Combine all segments into one text
    full_text = ""
    for segment in segments:
        full_text += segment.text + " "

    # Clean the text: remove punctuation and convert to lowercase
    cleaned_text = re.sub(r'[?.!,;:]', '', full_text.strip())
    cleaned_text = cleaned_text.lower()

    print(f"📝 Transcribed text: {cleaned_text}")
    return cleaned_text

def prepare_prompt(text: str, speaker: int, audio_path: str, sample_rate: int) -> Segment:
    """Prepare a voice prompt segment."""
    audio_tensor = load_prompt_audio(audio_path, sample_rate)
    return Segment(text=text, speaker=speaker, audio=audio_tensor)

@app.on_event("startup")
async def startup_event():
    """Initialize the models on startup."""
    global generator, whisper_model, device

    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"🚀 Starting API with device: {device}")

    # Load Whisper model for transcription
    print("Loading Whisper model...")
    whisper_load_start = time.time()
    whisper_model = WhisperModel("base", device=device, compute_type="float16")
    whisper_load_time = time.time() - whisper_load_start
    print(f"✅ Whisper model loaded in {whisper_load_time:.2f} seconds")

    # Load CSM model for voice generation
    print("Loading CSM model...")
    model_load_start = time.time()
    generator = load_csm_1b(device)
    model_load_time = time.time() - model_load_start
    print(f"✅ CSM model loaded in {model_load_time:.2f} seconds")

@app.get("/")
async def root():
    """Health check endpoint."""
    return {
        "message": "CSM Voice Generation API is running",
        "device": device,
        "csm_model_loaded": generator is not None,
        "whisper_model_loaded": whisper_model is not None
    }

@app.post("/generate-voice")
async def generate_voice(
    text: str = Form(..., description="Text to generate speech for"),
    voice_file: UploadFile = File(..., description="Voice sample file (WAV format)"),
    max_audio_length_ms: Optional[int] = Form(30000, description="Maximum audio length in milliseconds")
):
    """
    Generate speech using the provided voice sample.

    - **text**: The text you want to convert to speech
    - **voice_file**: Audio file containing the voice sample (WAV format recommended)
    - **max_audio_length_ms**: Maximum length of generated audio in milliseconds

    The voice sample will be automatically transcribed using Whisper.
    """
    if generator is None or whisper_model is None:
        raise HTTPException(status_code=503, detail="Models not loaded yet")

    # Validate file type
    if not voice_file.filename.lower().endswith(('.wav', '.mp3', '.flac', '.m4a')):
        raise HTTPException(status_code=400, detail="Voice file must be an audio file (WAV, MP3, FLAC, M4A)")

    try:
        # Save uploaded voice file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as temp_voice_file:
            content = await voice_file.read()
            temp_voice_file.write(content)
            temp_voice_path = temp_voice_file.name

        # Transcribe the voice file to get the text
        voice_text = transcribe_and_clean_audio(temp_voice_path)

        # Prepare the voice prompt
        print(f"🎵 Preparing voice prompt from uploaded file...")
        prompt_segment = prepare_prompt(
            text=voice_text,
            speaker=0,
            audio_path=temp_voice_path,
            sample_rate=generator.sample_rate
        )
        
        # Generate speech
        print(f"🎵 Generating speech for: {text[:50]}...")
        generation_start = time.time()
        
        audio_tensor = generator.generate(
            text=text,
            speaker=0,
            context=[prompt_segment],
            max_audio_length_ms=max_audio_length_ms,
        )
        
        generation_time = time.time() - generation_start
        print(f"✅ Speech generated in {generation_time:.2f} seconds")

        # Save generated audio to temporary file
        output_filename = f"generated_speech_{uuid.uuid4().hex[:8]}.wav"
        output_path = f"/tmp/{output_filename}"

        # Audio is already watermarked by the generator, just save it
        torchaudio.save(
            output_path,
            audio_tensor.unsqueeze(0).cpu(),
            generator.sample_rate
        )
        
        # Clean up temporary voice file
        os.unlink(temp_voice_path)
        
        return FileResponse(
            output_path,
            media_type="audio/wav",
            filename=output_filename,
            headers={"X-Generation-Time": str(generation_time)}
        )
        
    except Exception as e:
        # Clean up temporary files on error
        if 'temp_voice_path' in locals() and os.path.exists(temp_voice_path):
            os.unlink(temp_voice_path)
        raise HTTPException(status_code=500, detail=f"Error generating speech: {str(e)}")

@app.get("/health")
async def health_check():
    """Detailed health check."""
    return {
        "status": "healthy",
        "device": device,
        "model_loaded": generator is not None,
        "cuda_available": torch.cuda.is_available(),
        "gpu_memory": torch.cuda.get_device_properties(0).total_memory if torch.cuda.is_available() else None
    }

if __name__ == "__main__":
    uvicorn.run(
        "voice_api:app",
        host="0.0.0.0",
        port=8000,
        reload=False
    )
